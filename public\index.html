<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management System</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1><i class="fas fa-users"></i> User Management System</h1>
            <p>A modern web application with frontend and backend</p>
        </header>

        <!-- Navigation -->
        <nav class="nav">
            <button class="nav-btn active" data-section="dashboard">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </button>
            <button class="nav-btn" data-section="users">
                <i class="fas fa-users"></i> Users
            </button>
            <button class="nav-btn" data-section="add-user">
                <i class="fas fa-user-plus"></i> Add User
            </button>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Section -->
            <section id="dashboard" class="section active">
                <h2>Dashboard</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-users">0</h3>
                            <p>Total Users</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="server-status">Checking...</h3>
                            <p>Server Status</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="last-updated">Never</h3>
                            <p>Last Updated</p>
                        </div>
                    </div>
                </div>
                
                <div class="recent-activity">
                    <h3>Recent Activity</h3>
                    <div id="activity-log" class="activity-list">
                        <p class="no-activity">No recent activity</p>
                    </div>
                </div>
            </section>

            <!-- Users Section -->
            <section id="users" class="section">
                <div class="section-header">
                    <h2>Users</h2>
                    <button class="btn btn-primary" onclick="loadUsers()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                
                <div class="search-bar">
                    <input type="text" id="search-input" placeholder="Search users...">
                    <i class="fas fa-search"></i>
                </div>

                <div id="users-container" class="users-grid">
                    <div class="loading">Loading users...</div>
                </div>
            </section>

            <!-- Add User Section -->
            <section id="add-user" class="section">
                <h2>Add New User</h2>
                <form id="user-form" class="user-form">
                    <div class="form-group">
                        <label for="name">
                            <i class="fas fa-user"></i> Full Name
                        </label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">
                            <i class="fas fa-envelope"></i> Email Address
                        </label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="age">
                            <i class="fas fa-birthday-cake"></i> Age
                        </label>
                        <input type="number" id="age" name="age" min="1" max="120" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add User
                    </button>
                </form>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 User Management System. Built with HTML, CSS, and Node.js</p>
        </footer>
    </div>

    <!-- Modal for editing users -->
    <div id="edit-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit User</h3>
                <span class="close">&times;</span>
            </div>
            <form id="edit-form" class="user-form">
                <input type="hidden" id="edit-id">
                <div class="form-group">
                    <label for="edit-name">Full Name</label>
                    <input type="text" id="edit-name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="edit-email">Email Address</label>
                    <input type="email" id="edit-email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="edit-age">Age</label>
                    <input type="number" id="edit-age" name="age" min="1" max="120" required>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification">
        <span id="notification-message"></span>
        <button id="notification-close">&times;</button>
    </div>

    <script src="script.js"></script>
</body>
</html>
