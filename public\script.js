// Global variables
let users = [];
let activityLog = [];

// DOM elements
const navButtons = document.querySelectorAll('.nav-btn');
const sections = document.querySelectorAll('.section');
const userForm = document.getElementById('user-form');
const editForm = document.getElementById('edit-form');
const editModal = document.getElementById('edit-modal');
const searchInput = document.getElementById('search-input');
const notification = document.getElementById('notification');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    checkServerHealth();
    loadUsers();
});

// Initialize application
function initializeApp() {
    // Show dashboard by default
    showSection('dashboard');
    updateDashboard();
}

// Setup event listeners
function setupEventListeners() {
    // Navigation
    navButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const section = btn.dataset.section;
            showSection(section);
            
            // Update active nav button
            navButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
        });
    });

    // User form submission
    userForm.addEventListener('submit', handleUserSubmit);
    
    // Edit form submission
    editForm.addEventListener('submit', handleEditSubmit);
    
    // Search functionality
    searchInput.addEventListener('input', handleSearch);
    
    // Modal close events
    document.querySelector('.close').addEventListener('click', closeModal);
    window.addEventListener('click', (e) => {
        if (e.target === editModal) {
            closeModal();
        }
    });
    
    // Notification close
    document.getElementById('notification-close').addEventListener('click', hideNotification);
}

// Show specific section
function showSection(sectionName) {
    sections.forEach(section => {
        section.classList.remove('active');
    });
    document.getElementById(sectionName).classList.add('active');
    
    // Load data based on section
    if (sectionName === 'users') {
        loadUsers();
    } else if (sectionName === 'dashboard') {
        updateDashboard();
    }
}

// API Functions
async function apiCall(endpoint, method = 'GET', data = null) {
    try {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        if (data) {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(`/api${endpoint}`, options);
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.message || 'API request failed');
        }
        
        return result;
    } catch (error) {
        console.error('API Error:', error);
        showNotification(error.message, 'error');
        throw error;
    }
}

// Check server health
async function checkServerHealth() {
    try {
        const result = await apiCall('/health');
        document.getElementById('server-status').textContent = 'Online';
        document.getElementById('server-status').style.color = '#28a745';
    } catch (error) {
        document.getElementById('server-status').textContent = 'Offline';
        document.getElementById('server-status').style.color = '#dc3545';
    }
}

// Load users from API
async function loadUsers() {
    try {
        const container = document.getElementById('users-container');
        container.innerHTML = '<div class="loading">Loading users...</div>';
        
        const result = await apiCall('/users');
        users = result.data;
        
        displayUsers(users);
        updateDashboard();
        addActivity('Users loaded successfully');
    } catch (error) {
        document.getElementById('users-container').innerHTML = 
            '<div class="loading">Failed to load users</div>';
    }
}

// Display users in the UI
function displayUsers(usersToShow) {
    const container = document.getElementById('users-container');
    
    if (usersToShow.length === 0) {
        container.innerHTML = '<div class="loading">No users found</div>';
        return;
    }
    
    const usersHTML = usersToShow.map(user => `
        <div class="user-card">
            <div class="user-info">
                <h3>${escapeHtml(user.name)}</h3>
                <p><i class="fas fa-envelope"></i> ${escapeHtml(user.email)}</p>
                <p><i class="fas fa-birthday-cake"></i> ${user.age} years old</p>
            </div>
            <div class="user-actions">
                <button class="btn btn-primary btn-small" onclick="editUser(${user.id})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="btn btn-danger btn-small" onclick="deleteUser(${user.id})">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = usersHTML;
}

// Handle user form submission
async function handleUserSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(userForm);
    const userData = {
        name: formData.get('name'),
        email: formData.get('email'),
        age: parseInt(formData.get('age'))
    };
    
    try {
        const result = await apiCall('/users', 'POST', userData);
        showNotification('User created successfully!', 'success');
        userForm.reset();
        loadUsers();
        addActivity(`Created user: ${userData.name}`);
    } catch (error) {
        // Error already handled in apiCall
    }
}

// Edit user
function editUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;
    
    document.getElementById('edit-id').value = user.id;
    document.getElementById('edit-name').value = user.name;
    document.getElementById('edit-email').value = user.email;
    document.getElementById('edit-age').value = user.age;
    
    editModal.style.display = 'block';
}

// Handle edit form submission
async function handleEditSubmit(e) {
    e.preventDefault();
    
    const userId = document.getElementById('edit-id').value;
    const formData = new FormData(editForm);
    const userData = {
        name: formData.get('name'),
        email: formData.get('email'),
        age: parseInt(formData.get('age'))
    };
    
    try {
        const result = await apiCall(`/users/${userId}`, 'PUT', userData);
        showNotification('User updated successfully!', 'success');
        closeModal();
        loadUsers();
        addActivity(`Updated user: ${userData.name}`);
    } catch (error) {
        // Error already handled in apiCall
    }
}

// Delete user
async function deleteUser(userId) {
    if (!confirm('Are you sure you want to delete this user?')) {
        return;
    }
    
    try {
        const user = users.find(u => u.id === userId);
        const result = await apiCall(`/users/${userId}`, 'DELETE');
        showNotification('User deleted successfully!', 'success');
        loadUsers();
        addActivity(`Deleted user: ${user ? user.name : 'Unknown'}`);
    } catch (error) {
        // Error already handled in apiCall
    }
}

// Search functionality
function handleSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    const filteredUsers = users.filter(user => 
        user.name.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm)
    );
    displayUsers(filteredUsers);
}

// Modal functions
function closeModal() {
    editModal.style.display = 'none';
}

// Update dashboard
function updateDashboard() {
    document.getElementById('total-users').textContent = users.length;
    document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
    
    // Update activity log
    const activityContainer = document.getElementById('activity-log');
    if (activityLog.length === 0) {
        activityContainer.innerHTML = '<p class="no-activity">No recent activity</p>';
    } else {
        const activitiesHTML = activityLog.slice(-5).reverse().map(activity => 
            `<div class="activity-item">${activity}</div>`
        ).join('');
        activityContainer.innerHTML = activitiesHTML;
    }
}

// Add activity to log
function addActivity(message) {
    const timestamp = new Date().toLocaleString();
    activityLog.push(`${timestamp}: ${message}`);
    if (activityLog.length > 20) {
        activityLog = activityLog.slice(-20);
    }
}

// Notification system
function showNotification(message, type = 'success') {
    const notificationEl = document.getElementById('notification');
    const messageEl = document.getElementById('notification-message');
    
    messageEl.textContent = message;
    notificationEl.className = `notification ${type}`;
    notificationEl.style.display = 'flex';
    
    // Auto hide after 5 seconds
    setTimeout(hideNotification, 5000);
}

function hideNotification() {
    document.getElementById('notification').style.display = 'none';
}

// Utility function to escape HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
